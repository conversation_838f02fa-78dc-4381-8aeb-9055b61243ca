Storage module documentation

    storage/categories

        request body:
            None

        response body:
            {
                "success": true,
                "categories": [             # list of all categories
                    {
                        "size_category": 1,
                        "price": 100,
                        "is_available": true        # is there any available section of this category
                    },
                    {
                        "size_category": 2,
                        "price": 200,
                        "is_available": false
                    }
                ]
            }



    storage/sections

        request body:
            None

        response body:
            {
                "success": true,
                "products": [               # list of all sections
                    {
                        "section_id": 1,    # visual ID shown to user (e.g. "Section 1")
                        "identification_name": "Section 1",   # same as section_id, for compatibility with other modules
                        "tempered": 0,      # is the section tempered glass
                        "blocked": 0,       # is the section blocked
                        "service": 0,       # is the section a service section
                        "mode": "storage",  # operation mode of the section
                        "type": "storage",  # type of the section
                        "size_width": 50,   # size of the section in cm
                        "size_depth": 50,
                        "size_height": 50,
                        "size_category": 1,  # category of the section
                        "is_available": true    # is the section available for reservation
                    }
                ],
                "total_count": 1           # total number of sections
            }   



    storage/insert

        request body:
            {
                "section_id": 1,
                "size_category": 2,
                "email": "<EMAIL>"
            }

        response body:
            {
                "success": true,
                "session_id": "123e4567-e89b-12d3-a456-426614174000",     # session ID for WebSocket connection
                "status": "flow_started",                               # status of the operation
                "requires_payment": true,                                # does the operation require payment
                "amount": 100,                                          # amount to be paid
                "message": "Storage flow has been initiated, connect to WebSocket",   # message to be shown to user
                "section_id": 1                                         # section ID for the operation
            }

        functionality:
            - create websocked session and wait for command payment_screen_ready and then hardware_screen_ready


    storage/pickup
        request body:
            {
                "reservation_pin": "123456"
            }

        response body:
            {
                "success": true,
                "session_id": "123e4567-e89b-12d3-a456-426614174000",     # session ID for WebSocket connection
                "status": "flow_started",                               # status of the operation
                "requires_payment": true,                                # does the operation require payment
                "amount": 100,                                          # amount to be paid
                "message": "Storage flow has been initiated, connect to WebSocket",   # message to be shown to user
                "section_id": 1,                                        # section ID for the operation
                "hours_since_creation": 24,                             # hours since reservation creation
                "max_storage_hours": 25,                                # maximum storage hours
                "extra_charge_amount": 63                               # extra charge amount per hour
            }

        functionality:
            - create websocked session
            - if requires_payment == true, wait for command payment_screen_ready and then hardware_screen_ready
            - if requires_payment == false, wait only for hardware_screen_ready