#!/usr/bin/env python3
"""
Comprehensive test script for Order Module functionality.
Tests all order endpoints with various scenarios including mock responses.

Usage:
    python test_order_functionality.py

How to run:
    1. Start the FastAPI server:
       uvicorn main:app --reload

    2. Ensure EXTERNAL_API_ENABLE=false in .env file for mock responses

    3. Run the test script:
       python test_order_functionality.py

Requirements:
    - FastAPI server running on localhost:8000
    - EXTERNAL_API_ENABLE=false in .env for mock responses
    - Database with order_reservations table
    - Python packages: requests, websockets

What it tests:
    ✅ /order/employment/courier/pickup-expired - Pickup expired orders
    ✅ /order/employment/courier/pickup - Pickup employee orders
    ✅ /order/employment/courier/deliver - Deliver to employee
    ✅ /order/employment/customer/send - Employee send order
    ✅ /order/employment/customer/reclaim - Customer reclaim order
    ✅ /order/employment/customer/pickup - Customer pickup order
    ✅ WebSocket session creation and communication
    ✅ Mock response validation (valid/invalid inputs)
    ✅ Response structure validation
"""

import asyncio
import json
import time
import requests
import websockets
from typing import Dict, Any, Optional
import sys
import os

# Configuration
BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"
TIMEOUT = 10

class OrderTester:
    """Test class for order functionality"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        
        result = f"{status} | {test_name}"
        if message:
            result += f" | {message}"
        
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    def test_endpoint(self, endpoint: str, method: str = "POST", data: Dict = None) -> Optional[Dict]:
        """Test HTTP endpoint"""
        try:
            url = f"{BASE_URL}{endpoint}"
            
            if method == "POST":
                response = requests.post(url, json=data, timeout=TIMEOUT)
            elif method == "GET":
                response = requests.get(url, timeout=TIMEOUT)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"HTTP {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"Request error: {e}")
            return None
    
    async def test_websocket_session(self, session_id: str, test_messages: list = None) -> bool:
        """Test WebSocket session"""
        try:
            uri = f"{WS_BASE_URL}/ws/{session_id}"
            
            async with websockets.connect(uri) as websocket:
                # Wait for initial connection message
                initial_msg = await asyncio.wait_for(websocket.recv(), timeout=5)
                print(f"WebSocket connected: {initial_msg}")
                
                # Send test messages if provided
                if test_messages:
                    for msg in test_messages:
                        await websocket.send(json.dumps(msg))
                        response = await asyncio.wait_for(websocket.recv(), timeout=5)
                        print(f"WS Response: {response}")
                
                return True
                
        except Exception as e:
            print(f"WebSocket error: {e}")
            return False
    
    def test_pickup_expired_orders(self):
        """Test /order/employment/courier/pickup-expired endpoint"""
        print("\n🔍 Testing Pickup Expired Orders...")
        
        # Test with valid operator ID
        data = {"operator_id": 1}
        response = self.test_endpoint("/order/employment/courier/pickup-expired", data=data)
        
        if response:
            expected_fields = ["session_id", "success", "message", "sections", "total_sections"]
            has_all_fields = all(field in response for field in expected_fields)
            
            if has_all_fields and response["success"]:
                self.log_test("Pickup Expired - Valid Request", True, f"Found {response['total_sections']} sections")
                
                # Test WebSocket if session created
                if response["session_id"]:
                    ws_success = asyncio.run(self.test_websocket_session(response["session_id"]))
                    self.log_test("Pickup Expired - WebSocket", ws_success)
            else:
                self.log_test("Pickup Expired - Valid Request", False, "Missing fields or failed")
        else:
            self.log_test("Pickup Expired - Valid Request", False, "No response")
    
    def test_pickup_employee_orders(self):
        """Test /order/employment/courier/pickup endpoint"""
        print("\n🔍 Testing Pickup Employee Orders...")
        
        # Test with valid operator ID
        data = {"operator_id": 2}
        response = self.test_endpoint("/order/employment/courier/pickup", data=data)
        
        if response:
            expected_fields = ["session_id", "success", "message", "sections", "total_sections"]
            has_all_fields = all(field in response for field in expected_fields)
            
            if has_all_fields and response["success"]:
                self.log_test("Pickup Employee - Valid Request", True, f"Found {response['total_sections']} sections")
                
                # Test WebSocket if session created
                if response["session_id"]:
                    ws_success = asyncio.run(self.test_websocket_session(response["session_id"]))
                    self.log_test("Pickup Employee - WebSocket", ws_success)
            else:
                self.log_test("Pickup Employee - Valid Request", False, "Missing fields or failed")
        else:
            self.log_test("Pickup Employee - Valid Request", False, "No response")
    
    def test_deliver_to_employee(self):
        """Test /order/employment/courier/deliver endpoint"""
        print("\n🔍 Testing Deliver to Employee...")
        
        # Test with valid phone number (mock response)
        data = {"phone_number": "123456789"}
        response = self.test_endpoint("/order/employment/courier/deliver", data=data)
        
        if response:
            expected_fields = ["session_id", "success", "message", "section_ids"]
            has_all_fields = all(field in response for field in expected_fields)

            if has_all_fields and response["success"]:
                section_ids = response.get("section_ids")
                if section_ids:
                    self.log_test("Deliver - Valid Phone", True, f"Phone validated successfully with {len(section_ids)} reserved sections")
                else:
                    self.log_test("Deliver - Valid Phone", True, "Phone validated successfully with no pre-reserved sections")

                # Test WebSocket if session created
                if response["session_id"]:
                    ws_success = asyncio.run(self.test_websocket_session(response["session_id"]))
                    self.log_test("Deliver - WebSocket", ws_success)
            else:
                self.log_test("Deliver - Valid Phone", False, "Validation failed or missing fields")
        else:
            self.log_test("Deliver - Valid Phone", False, "No response")
        
        # Test with invalid phone number
        data = {"phone_number": "invalid"}
        response = self.test_endpoint("/order/employment/courier/deliver", data=data)
        
        if response and not response["success"]:
            self.log_test("Deliver - Invalid Phone", True, "Correctly rejected invalid phone")
        else:
            self.log_test("Deliver - Invalid Phone", False, "Should reject invalid phone")
    
    def test_employee_send_order(self):
        """Test /order/employment/customer/send endpoint"""
        print("\n🔍 Testing Employee Send Order...")
        
        # Test with valid phone number with pre-reserved section (mock response)
        data = {"phone_number": "123456789"}
        response = self.test_endpoint("/order/employment/customer/send", data=data)
        
        if response:
            expected_fields = ["session_id", "success", "section_id", "valid", "message"]
            has_all_fields = all(field in response for field in expected_fields)
            
            if has_all_fields and response["success"] and response["valid"]:
                self.log_test("Send - Valid Phone (Pre-reserved)", True, f"Section: {response['section_id']}")
                
                # Test WebSocket if session created
                if response["session_id"]:
                    ws_success = asyncio.run(self.test_websocket_session(response["session_id"]))
                    self.log_test("Send - WebSocket", ws_success)
            else:
                self.log_test("Send - Valid Phone (Pre-reserved)", False, "Validation failed")
        else:
            self.log_test("Send - Valid Phone (Pre-reserved)", False, "No response")
        
        # Test with valid phone number without pre-reserved section
        data = {"phone_number": "987654321"}
        response = self.test_endpoint("/order/employment/customer/send", data=data)
        
        if response and response["success"] and response["valid"] and response["section_id"] is None:
            self.log_test("Send - Valid Phone (No Pre-reservation)", True, "No section pre-reserved")
        else:
            self.log_test("Send - Valid Phone (No Pre-reservation)", False, "Unexpected response")
        
        # Test with invalid phone number
        data = {"phone_number": "invalid"}
        response = self.test_endpoint("/order/employment/customer/send", data=data)
        
        if response and not response["valid"]:
            self.log_test("Send - Invalid Phone", True, "Correctly rejected invalid phone")
        else:
            self.log_test("Send - Invalid Phone", False, "Should reject invalid phone")
    
    def test_customer_reclaim_order(self):
        """Test /order/employment/customer/reclaim endpoint"""
        print("\n🔍 Testing Customer Reclaim Order...")
        
        # Test with valid reclamation PIN (mock response)
        data = {"reclamation_pin": "RECLAIM123"}
        response = self.test_endpoint("/order/employment/customer/reclaim", data=data)
        
        if response:
            expected_fields = ["session_id", "success", "section_id", "valid", "message"]
            has_all_fields = all(field in response for field in expected_fields)
            
            if has_all_fields and response["success"] and response["valid"]:
                self.log_test("Reclaim - Valid PIN (Pre-reserved)", True, f"Section: {response['section_id']}")
                
                # Test WebSocket if session created
                if response["session_id"]:
                    ws_success = asyncio.run(self.test_websocket_session(response["session_id"]))
                    self.log_test("Reclaim - WebSocket", ws_success)
            else:
                self.log_test("Reclaim - Valid PIN (Pre-reserved)", False, "Validation failed")
        else:
            self.log_test("Reclaim - Valid PIN (Pre-reserved)", False, "No response")
        
        # Test with valid PIN without pre-reserved section
        data = {"reclamation_pin": "RECLAIM456"}
        response = self.test_endpoint("/order/employment/customer/reclaim", data=data)
        
        if response and response["success"] and response["valid"] and response["section_id"] is None:
            self.log_test("Reclaim - Valid PIN (No Pre-reservation)", True, "No section pre-reserved")
        else:
            self.log_test("Reclaim - Valid PIN (No Pre-reservation)", False, "Unexpected response")
        
        # Test with invalid reclamation PIN
        data = {"reclamation_pin": "invalid"}
        response = self.test_endpoint("/order/employment/customer/reclaim", data=data)
        
        if response and not response["valid"]:
            self.log_test("Reclaim - Invalid PIN", True, "Correctly rejected invalid PIN")
        else:
            self.log_test("Reclaim - Invalid PIN", False, "Should reject invalid PIN")
    
    def test_customer_pickup_order(self):
        """Test /order/employment/customer/pickup endpoint"""
        print("\n🔍 Testing Customer Pickup Order...")
        
        # Note: This test requires actual data in order_reservations table
        # For now, test with a dummy PIN to check endpoint structure
        data = {"pickup_pin": "123456"}
        response = self.test_endpoint("/order/employment/customer/pickup", data=data)
        
        if response:
            expected_fields = ["session_id", "success", "message", "section_id", "requires_payment", "amount"]
            has_all_fields = all(field in response for field in expected_fields)
            
            if has_all_fields:
                if response["success"]:
                    self.log_test("Pickup - Valid PIN", True, f"Section: {response['section_id']}")
                else:
                    self.log_test("Pickup - Invalid PIN", True, "Correctly rejected invalid PIN")
            else:
                self.log_test("Pickup - Response Structure", False, "Missing required fields")
        else:
            self.log_test("Pickup - Response Structure", False, "No response")
    
    def run_all_tests(self):
        """Run all order functionality tests"""
        print("🚀 Starting Order Module Functionality Tests")
        print("=" * 60)
        
        # Check if server is running
        try:
            response = requests.get(f"{BASE_URL}/docs", timeout=5)
            if response.status_code != 200:
                print("❌ FastAPI server is not running on localhost:8000")
                return
        except:
            print("❌ Cannot connect to FastAPI server on localhost:8000")
            print("Please start the server with: uvicorn main:app --reload")
            return
        
        print("✅ FastAPI server is running")
        
        # Run all tests
        self.test_pickup_expired_orders()
        self.test_pickup_employee_orders()
        self.test_deliver_to_employee()
        self.test_employee_send_order()
        self.test_customer_reclaim_order()
        self.test_customer_pickup_order()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.passed_tests == self.total_tests:
            print("\n🎉 All tests passed! Order module is working correctly.")
        else:
            print(f"\n⚠️  {self.total_tests - self.passed_tests} test(s) failed. Check the output above.")

def main():
    """Main function"""
    print("Order Module Functionality Tester")
    print("Make sure:")
    print("1. FastAPI server is running: uvicorn main:app --reload")
    print("2. EXTERNAL_API_ENABLE=false in .env for mock responses")
    print("3. Database is accessible")
    print()
    
    tester = OrderTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
