"""
Order Step Handlers.
Handles individual steps in order flows.
"""

import logging
import async<PERSON>
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from managers.ws_manager import ws_manager
from managers.sequence_manager import sequence_manager
from managers.session_manager import session_manager
from hardware.locker_control import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.section_repository import SectionRepository
from config import device_config

logger = logging.getLogger(__name__)

class StepHandler(ABC):
    """Base class for step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.logger = logging.getLogger(__name__)
    
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket connection is active"""
        return ws_manager.is_connected(self.session_id)
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False

        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            return False

    def get_operator_id(self) -> Optional[int]:
        """Get operator_id from session data"""
        try:
            session = session_manager.get_session(self.session_id)
            if session:
                return getattr(session, 'operator_id', None)
            return None
        except Exception as e:
            logger.error(f"Error getting operator_id from session {self.session_id}: {e}")
            return None
    
    async def wait_for_websocket_ready(self, step_type: str) -> bool:
        """Wait for WebSocket ready signal"""
        try:
            # Send step ready signal
            await self.send_websocket_message({
                "type": "step_ready",
                "step": step_type,
                "message": f"Ready for {step_type}"
            })
            return True
        except Exception as e:
            logger.error(f"Error waiting for WebSocket ready: {e}")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class PickupLoopHandler(StepHandler):
    """Handler for pickup loop operations"""

    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.picking_up = False
        self.available_sections = []
        self.message_queue = asyncio.Queue()

    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute pickup loop for multiple sections"""
        try:
            if not await self.wait_for_websocket_ready("pickup_loop"):
                return False

            sections = context.get('sections', [])
            operation = context.get('operation', 'pickup')
            self.available_sections = sections

            await self.send_websocket_message({
                "type": "pickup_loop_started",
                "sections": sections,
                "operation": operation,
                "message": f"Starting pickup loop for {len(sections)} sections"
            })

            # Start pickup loop
            await self._employment_pickup_loop(sections)

            return True

        except Exception as e:
            logger.error(f"Error in pickup loop handler: {e}")
            await self.send_websocket_message({
                "type": "pickup_loop_error",
                "message": f"Error in pickup loop: {str(e)}"
            })
            return False

    async def handle_message(self, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message"""
        try:
            if not self.picking_up:
                logger.warning(f"Received message before pickup started: {message.get('type')}")
                return False

            # Add message to queue for processing by pickup loop
            await self.message_queue.put(message)
            return True

        except Exception as e:
            logger.error(f"Error handling message in pickup loop: {e}")
            return False
    
    async def _employment_pickup_loop(self, sections: List[int]):
        """Employment pickup loop implementation with proper door control and message handling"""
        self.picking_up = True
        locker_controller = LockerController()

        # Send initial status
        await self.send_websocket_message({
            "type": "pickup_status",
            "message": "Waiting for section selection",
            "available_sections": sections
        })

        while self.picking_up:
            try:
                # Wait for message from WebSocket with timeout
                try:
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=30.0)
                except asyncio.TimeoutError:
                    logger.warning(f"Pickup loop timeout for session {self.session_id}")
                    await self.send_websocket_message({
                        "type": "pickup_timeout",
                        "message": "Pickup operation timed out"
                    })
                    break

                message_type = message.get("type")
                logger.info(f"Processing pickup loop message: {message_type}")

                if message_type == "open_section":
                    section_id = message.get("section_id")
                    if not section_id:
                        await self.send_websocket_message({
                            "type": "error",
                            "message": "Section ID is required for open_section"
                        })
                        continue

                    if section_id not in sections:
                        await self.send_websocket_message({
                            "type": "error",
                            "message": f"Section {section_id} is not available for pickup"
                        })
                        continue

                    success = await self._handle_pickup_section_open(section_id, locker_controller)
                    if success:
                        # Remove section from available sections after successful pickup
                        sections.remove(section_id)
                        await self.send_websocket_message({
                            "type": "pickup_status",
                            "message": f"Section {section_id} picked up successfully. Available sections: {sections}",
                            "available_sections": sections
                        })

                        # If no more sections, end the loop
                        if not sections:
                            await self.send_websocket_message({
                                "type": "pickup_completed",
                                "message": "All sections have been picked up"
                            })
                            self.picking_up = False

                elif message_type == "storno":
                    logger.info(f"Pickup cancelled for session {self.session_id}")
                    await self.send_websocket_message({
                        "type": "pickup_cancelled",
                        "message": "Pickup operation cancelled"
                    })
                    self.picking_up = False

                else:
                    logger.warning(f"Unknown message type in pickup loop: {message_type}")
                    await self.send_websocket_message({
                        "type": "error",
                        "message": f"Unknown message type: {message_type}"
                    })

            except Exception as e:
                logger.error(f"Error in pickup loop: {e}")
                await self.send_websocket_message({
                    "type": "pickup_error",
                    "message": f"Error in pickup: {str(e)}"
                })
                break

    async def _handle_pickup_section_open(self, section_id: int, locker_controller: LockerController) -> bool:
        """Handle opening a section during pickup with proper door state checking"""
        try:
            # Get operator_id from session
            operator_id = self.get_operator_id()

            # Open the door
            await self.send_websocket_message({
                "type": "section_opening",
                "section_id": section_id,
                "message": f"Opening section {section_id}"
            })

            success = await locker_controller.unlock_locker(section_id, mode="order", operator_id=operator_id)
            if not success:
                await self.send_websocket_message({
                    "type": "section_error",
                    "section_id": section_id,
                    "message": f"Failed to open section {section_id}"
                })
                return False

            await self.send_websocket_message({
                "type": "section_opened",
                "section_id": section_id,
                "message": f"Section {section_id} opened. Please remove item and close door."
            })

            # Wait for door to be closed
            await self._wait_for_door_close(section_id, locker_controller)

            await self.send_websocket_message({
                "type": "section_closed",
                "section_id": section_id,
                "message": f"Section {section_id} closed successfully"
            })

            return True

        except Exception as e:
            logger.error(f"Error handling pickup section open for {section_id}: {e}")
            await self.send_websocket_message({
                "type": "section_error",
                "section_id": section_id,
                "message": f"Error opening section {section_id}: {str(e)}"
            })
            return False

    async def _wait_for_door_close(self, section_id: int, locker_controller: LockerController):
        """Wait for door to be closed using door state checking"""
        try:
            # Get timeout from config
            close_timeout = device_config.fsm_config.get("door_close_timeout", 30)

            await self.send_websocket_message({
                "type": "waiting_door_close",
                "section_id": section_id,
                "message": f"Waiting for door {section_id} to close"
            })

            # Use sequence_manager's _wait_for_door_state function
            door_closed = await sequence_manager._wait_for_door_state(
                section_id=section_id,
                lock_id=section_id,  # Assuming section_id == lock_id
                expected_open=False,  # We want the door to be closed
                timeout=close_timeout,
                is_tempered=True,  # Assuming tempered locks
                session_id=self.session_id,
                mode="order"
            )

            if not door_closed:
                await self.send_websocket_message({
                    "type": "door_timeout",
                    "section_id": section_id,
                    "message": f"Timeout waiting for door {section_id} to close"
                })

        except Exception as e:
            logger.error(f"Error waiting for door close for section {section_id}: {e}")
            await self.send_websocket_message({
                "type": "door_error",
                "section_id": section_id,
                "message": f"Error waiting for door close: {str(e)}"
            })

class SectionSelectionHandler(StepHandler):
    """Handler for section selection operations"""

    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.selecting = False
        self.selected_section_ids = []
        self.operation = None
        self.phone_number = None
        self.selection_started = False
        self.waiting_for_insertion = False
        self.current_section_id = None

    async def handle_message(self, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message"""
        try:
            if not self.selection_started:
                logger.warning(f"Received message before selection started: {message.get('type')}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing section selection message: {message_type}")

            if message_type == "open_section":
                return await self._handle_open_section(message)
            elif message_type == "stop_selection":
                return await self._handle_stop_selection()
            elif "inserted" in message:
                return await self._handle_insertion_confirmation(message)
            else:
                logger.warning(f"Unknown message type in section selection: {message_type}")
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                return False

        except Exception as e:
            logger.error(f"Error handling message in section selection: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False

    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute section selection - start the selection process"""
        try:
            if not await self.wait_for_websocket_ready("section_selection"):
                return False

            self.operation = context.get('operation')
            self.phone_number = context.get('phone_number')
            self.reservation_pin = context.get('reservation_pin')  # For customer_send operation
            reserved_section_id = context.get('reserved_section_id')  # Pre-reserved section from jetveo (for employee_send)
            reserved_section_ids = context.get('reserved_section_ids')  # Pre-reserved sections from jetveo (for deliver_employee)

            await self.send_websocket_message({
                "type": "section_selection_started",
                "operation": self.operation,
                "reserved_section_id": reserved_section_id,
                "reserved_section_ids": reserved_section_ids,
                "message": "Starting section selection"
            })

            # Start the selection process - send initial status and wait for user interaction
            await self.send_websocket_message({
                "type": "selection_status",
                "message": "Waiting for section selection. Send 'open_section' to select or 'stop_selection' to finish.",
                "selected_sections": self.selected_section_ids
            })

            self.selecting = True
            self.selection_started = True

            # Return False to indicate the step is not complete yet - it will complete when user finishes selection
            return False

        except Exception as e:
            logger.error(f"Error in section selection handler: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False

    async def _handle_open_section(self, message: Dict[str, Any]) -> bool:
        """Handle open_section message according to task.py logic"""
        try:
            section_id = message.get("section_id")
            if not section_id:
                await self.send_websocket_message({
                    "type": "error",
                    "message": "Section ID is required for open_section"
                })
                return False

            locker_controller = LockerController()

            # Get operator_id from session
            operator_id = self.get_operator_id()

            # Step 1: Open the door
            success = await locker_controller.unlock_locker(section_id, mode="order", operator_id=operator_id)
            if not success:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Failed to open section {section_id}"
                })
                return False

            # Step 2: Send "opened" status
            await self.send_websocket_message({
                "section_id": section_id,
                "status": "opened"
            })

            # Step 3: Wait for door to be closed (simulate door state checking)
            await self._wait_for_door_close(section_id)

            # Step 4: Send "closed" status
            await self.send_websocket_message({
                "section_id": section_id,
                "status": "closed"
            })

            # Step 5: Now wait for insertion confirmation message from client
            # This will be handled by _handle_insertion_confirmation when client sends {"inserted": true/false}
            self.waiting_for_insertion = True
            self.current_section_id = section_id

            return True

        except Exception as e:
            logger.error(f"Error handling open_section: {e}")
            await self.send_websocket_message({
                "type": "error",
                "message": f"Error opening section: {str(e)}"
            })
            return False

    async def _handle_insertion_confirmation(self, message: Dict[str, Any]) -> bool:
        """Handle insertion confirmation message {"inserted": true/false}"""
        try:
            if not self.waiting_for_insertion:
                await self.send_websocket_message({
                    "type": "error",
                    "message": "Not waiting for insertion confirmation"
                })
                return False

            inserted = message.get("inserted", False)
            section_id = self.current_section_id

            if inserted:
                # Add section to selected sections
                self.selected_section_ids.append(section_id)

                # For customer_send, we only allow one section selection, so complete immediately
                if self.operation == "customer_send":
                    # Complete the selection process
                    success = await self._create_reservations()

                    if success:
                        await self.send_websocket_message({
                            "order_send": True,
                            "section_ids": self.selected_section_ids,
                            "message": "Order processed successfully"
                        })
                    else:
                        await self.send_websocket_message({
                            "order_send": False,
                            "message": "Failed to update reservation"
                        })

                    # Complete the step
                    await self._complete_step()
                    return True
                else:
                    # For other operations, continue selection
                    await self.send_websocket_message({
                        "message": "Section selected. Send 'open_section' for more or 'stop_selection' to finish."
                    })
            else:
                # Item not inserted, wait for new message
                await self.send_websocket_message({
                    "message": "waiting for new message"
                })

            # Reset waiting state
            self.waiting_for_insertion = False
            self.current_section_id = None

            return True

        except Exception as e:
            logger.error(f"Error handling insertion confirmation: {e}")
            await self.send_websocket_message({
                "type": "error",
                "message": f"Error handling insertion: {str(e)}"
            })
            return False

    async def _wait_for_door_close(self, section_id: int):
        """Wait for door to be closed (simulate door state checking)"""
        try:
            # Simulate waiting for door to close
            # In real implementation, this would check actual door state
            await asyncio.sleep(1)  # Simulate door close detection

        except Exception as e:
            logger.error(f"Error waiting for door close: {e}")

    async def _wait_for_door_close_and_insertion(self, section_id: int):
        """Wait for door to be closed and handle insertion confirmation"""
        try:
            # Get timeout from config
            close_timeout = device_config.fsm_config.get("door_close_timeout", 30)

            await self.send_websocket_message({
                "type": "waiting_door_close",
                "section_id": section_id,
                "message": f"Waiting for door {section_id} to close"
            })

            # Use sequence_manager's _wait_for_door_state function
            door_closed = await sequence_manager._wait_for_door_state(
                section_id=section_id,
                lock_id=section_id,  # Assuming section_id == lock_id
                expected_open=False,  # We want the door to be closed
                timeout=close_timeout,
                is_tempered=True,  # Assuming tempered locks
                session_id=self.session_id,
                mode="order"
            )

            if door_closed:
                await self.send_websocket_message({
                    "type": "section_closed",
                    "section_id": section_id,
                    "message": f"Section {section_id} closed. Waiting for insertion confirmation."
                })

                # Wait for insertion confirmation message
                # This would be handled by the WebSocket handler in real implementation
                # For now, we assume insertion is successful
                await self.send_websocket_message({
                    "type": "waiting_insertion",
                    "section_id": section_id,
                    "message": "Please confirm item insertion by sending {\"inserted\": true}"
                })

            else:
                await self.send_websocket_message({
                    "type": "door_timeout",
                    "section_id": section_id,
                    "message": f"Timeout waiting for door {section_id} to close"
                })

        except Exception as e:
            logger.error(f"Error waiting for door close and insertion for section {section_id}: {e}")
            await self.send_websocket_message({
                "type": "door_error",
                "section_id": section_id,
                "message": f"Error waiting for door close: {str(e)}"
            })

    async def _handle_stop_selection(self) -> bool:
        """Handle stop_selection message according to task.py logic"""
        try:
            logger.info(f"Stop selection requested for session {self.session_id}")
            self.selecting = False

            # According to task.py: if stop_selection, return false, null (no sections selected)
            # Determine message type based on operation
            if self.operation == "deliver_employee":
                message_key = "order_deliver"
            else:  # employee_send, customer_reclaim, or customer_send
                message_key = "order_send"

            await self.send_websocket_message({
                message_key: False,
                "message": "Selection stopped without selecting any sections"
            })

            # Complete the step by notifying the flow coordinator
            await self._complete_step()
            return True

        except Exception as e:
            logger.error(f"Error handling stop_selection: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error stopping selection: {str(e)}"
            })
            return False

    async def _create_reservations(self) -> bool:
        """Create reservations for selected sections"""
        try:
            from infrastructure.repositories.order_repository import OrderRepository

            repo = OrderRepository()
            all_results = []

            if self.operation == "customer_send":
                # For customer_send, we only select one section and update existing reservation
                if len(self.selected_section_ids) != 1:
                    return False

                section_id = self.selected_section_ids[0]

                # Find the reservation by PIN and update it
                reservation_result = repo.find_reservation_by_pin_and_status(self.reservation_pin, 8)
                if not reservation_result["success"]:
                    return False

                reservation = reservation_result["reservation"]
                # Update reservation with selected section and change status to 1
                success = repo.update_reservation_section_and_status(reservation["id"], section_id, 1)
                return success
            else:
                # For other operations, create new reservations
                for section_id in self.selected_section_ids:
                    if self.operation == "deliver_employee":
                        result = repo.create_employee_delivery_reservation(self.phone_number, section_id)
                    elif self.operation == "employee_send":
                        result = repo.create_employee_send_reservation(self.phone_number, section_id)
                    elif self.operation == "customer_reclaim":
                        result = repo.create_employee_send_reservation(self.phone_number, section_id)
                    else:
                        result = {"success": False, "error": "Unknown operation"}

                    all_results.append(result)

                # Check if all reservations were successful
                all_successful = all(result["success"] for result in all_results)
                return all_successful

        except Exception as e:
            logger.error(f"Error creating reservations: {e}")
            return False

    async def _complete_step(self):
        """Complete the current step and move to next step in flow"""
        from domains.order.flow_coordinator import flow_coordinator

        self.selection_started = False
        self.selecting = False

        # Notify the flow coordinator to complete this step and move to the next
        await flow_coordinator.complete_current_step_and_continue(self.session_id)

    async def _select_sections(self) -> tuple[bool, Optional[List[int]]]:
        """Section selection implementation - supports multiple sections"""
        selected_section_ids = []
        selecting = True
        locker_controller = LockerController()

        # Send initial status
        await self.send_websocket_message({
            "type": "selection_status",
            "message": "Waiting for section selection. Send 'open_section' to select or 'stop_selection' to finish.",
            "selected_sections": selected_section_ids
        })

        while selecting:
            try:
                # Wait for message from WebSocket with timeout
                try:
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=30.0)
                except asyncio.TimeoutError:
                    logger.warning(f"Section selection timeout for session {self.session_id}")
                    await self.send_websocket_message({
                        "type": "selection_timeout",
                        "message": "Selection timed out"
                    })
                    return False, None

                message_type = message.get("type")
                logger.info(f"Processing section selection message: {message_type}")

                if message_type == "open_section":
                    section_id = message.get("section_id")
                    if not section_id:
                        await self.send_websocket_message({
                            "type": "error",
                            "message": "Section ID is required for open_section"
                        })
                        continue

                    # Open the door using actual hardware control
                    await self.send_websocket_message({
                        "type": "section_opening",
                        "section_id": section_id,
                        "message": f"Opening section {section_id}"
                    })

                    # Get operator_id from session
                    operator_id = self.get_operator_id()
                    success = await locker_controller.unlock_locker(section_id, mode="order", operator_id=operator_id)
                    if not success:
                        await self.send_websocket_message({
                            "type": "section_error",
                            "section_id": section_id,
                            "message": f"Failed to open section {section_id}"
                        })
                        continue

                    await self.send_websocket_message({
                        "type": "section_opened",
                        "section_id": section_id,
                        "message": f"Section {section_id} opened. Please insert item and close door."
                    })

                    # Wait for door to be closed using proper door state checking
                    try:
                        await self._wait_for_door_close_and_insertion_in_selection(section_id)

                        # Add to selected sections after successful insertion
                        selected_section_ids.append(section_id)

                        await self.send_websocket_message({
                            "type": "section_selected",
                            "section_id": section_id,
                            "selected_sections": selected_section_ids,
                            "message": f"Section {section_id} selected successfully. Send 'open_section' for more or 'stop_selection' to finish."
                        })

                    except Exception as e:
                        logger.error(f"Error waiting for door close in selection for section {section_id}: {e}")
                        await self.send_websocket_message({
                            "type": "section_error",
                            "section_id": section_id,
                            "message": f"Error with section {section_id}: {str(e)}"
                        })

                elif message_type == "stop_selection":
                    logger.info(f"Stop selection requested for session {self.session_id}")
                    selecting = False

                    if selected_section_ids:
                        await self.send_websocket_message({
                            "type": "selection_completed",
                            "selected_sections": selected_section_ids,
                            "message": f"Selection completed with {len(selected_section_ids)} sections"
                        })
                        return True, selected_section_ids
                    else:
                        await self.send_websocket_message({
                            "type": "selection_cancelled",
                            "message": "Selection cancelled - no sections selected"
                        })
                        return False, None

                else:
                    logger.warning(f"Unknown message type in section selection: {message_type}")
                    await self.send_websocket_message({
                        "type": "error",
                        "message": f"Unknown message type: {message_type}"
                    })

            except Exception as e:
                logger.error(f"Error in section selection: {e}")
                await self.send_websocket_message({
                    "type": "selection_error",
                    "message": f"Error in section selection: {str(e)}"
                })
                return False, None

        return True, selected_section_ids

    async def _wait_for_door_close_and_insertion_in_selection(self, section_id: int):
        """Wait for door to be closed and handle insertion confirmation in selection process"""
        try:
            # Get timeout from config
            close_timeout = device_config.fsm_config.get("door_close_timeout", 30)

            await self.send_websocket_message({
                "type": "waiting_door_close",
                "section_id": section_id,
                "message": f"Waiting for door {section_id} to close"
            })

            # Use sequence_manager's _wait_for_door_state function
            door_closed = await sequence_manager._wait_for_door_state(
                section_id=section_id,
                lock_id=section_id,  # Assuming section_id == lock_id
                expected_open=False,  # We want the door to be closed
                timeout=close_timeout,
                is_tempered=True,  # Assuming tempered locks
                session_id=self.session_id,
                mode="order"
            )

            if door_closed:
                await self.send_websocket_message({
                    "type": "section_closed",
                    "section_id": section_id,
                    "message": f"Section {section_id} closed. Item insertion confirmed."
                })
            else:
                await self.send_websocket_message({
                    "type": "door_timeout",
                    "section_id": section_id,
                    "message": f"Timeout waiting for door {section_id} to close"
                })
                raise Exception(f"Door close timeout for section {section_id}")

        except Exception as e:
            logger.error(f"Error waiting for door close and insertion in selection for section {section_id}: {e}")
            await self.send_websocket_message({
                "type": "door_error",
                "section_id": section_id,
                "message": f"Error waiting for door close: {str(e)}"
            })
            raise

class HardwareHandler(StepHandler):
    """Handler for hardware operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute hardware operation"""
        try:
            if not await self.wait_for_websocket_ready("hardware"):
                return False
            
            section_id = context.get('section_id')
            operation = context.get('operation', 'open_for_pickup')
            reservation_id = context.get('reservation_id')
            
            await self.send_websocket_message({
                "type": "hardware_started",
                "section_id": section_id,
                "operation": operation,
                "message": f"Starting hardware operation for section {section_id}"
            })
            
            # Open the section
            locker_controller = LockerController()

            # Get operator_id from session
            operator_id = self.get_operator_id()
            success = await locker_controller.unlock_locker(section_id, mode="order", operator_id=operator_id)
            
            if success:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": True,
                    "section_id": section_id,
                    "message": f"Section {section_id} opened successfully"
                })
                
                # Update reservation status if this is a pickup
                if reservation_id:
                    repo = OrderRepository()
                    repo.update_reservation_status(reservation_id, 0)  # Mark as completed
            else:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": False,
                    "section_id": section_id,
                    "message": f"Failed to open section {section_id}"
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Error in hardware handler: {e}")
            await self.send_websocket_message({
                "type": "hardware_error",
                "message": f"Error in hardware operation: {str(e)}"
            })
            return False

def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "pickup_loop": PickupLoopHandler,
        "section_selection": SectionSelectionHandler,
        "hardware": HardwareHandler
    }
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    return None
