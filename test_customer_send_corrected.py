#!/usr/bin/env python3
"""
Test script for the corrected /order/customer/send endpoint.
Tests the customer send order functionality with the correct WebSocket flow.
"""

import asyncio
import json
import websockets
import requests
import time

BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

def test_customer_send_endpoint():
    """Test the /order/customer/send endpoint"""
    print("🧪 Testing /order/customer/send endpoint...")
    
    try:
        # Test with a reservation PIN (string)
        print("📞 Testing with reservation PIN '123456'...")
        response = requests.post(f"{BASE_URL}/order/customer/send", 
                               json={"reservation_pin": "123456"})
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📥 Response data: {json.dumps(data, indent=2)}")
            
            expected_fields = ["session_id", "success", "section_id", "message"]
            has_all_fields = all(field in data for field in expected_fields)
            
            if has_all_fields:
                print("✅ Response has all expected fields")
                
                if data["success"]:
                    print(f"✅ Request successful: {data['message']}")
                    if data["session_id"]:
                        print(f"✅ Session ID created: {data['session_id']}")
                        return data["session_id"]
                    else:
                        print("⚠️  No session ID returned")
                else:
                    print(f"ℹ️  Request failed (expected for test): {data['message']}")
            else:
                print("❌ Response missing expected fields")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
    
    return None

async def test_websocket_flow(session_id):
    """Test the correct WebSocket flow according to task.py"""
    print(f"\n🔌 Testing WebSocket flow for session {session_id}...")
    
    try:
        ws_url = f"{WS_BASE_URL}/ws/order/{session_id}"
        print(f"🔗 Connecting to: {ws_url}")
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected")
            
            # Wait for initial messages
            print("📨 Waiting for initial messages...")
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    print(f"📥 Initial message {i+1}: {data}")
                except asyncio.TimeoutError:
                    print(f"⏰ Timeout waiting for initial message {i+1}")
                    break
            
            print("\n" + "="*50)
            print("🎯 CORRECT WEBSOCKET FLOW ACCORDING TO TASK.PY")
            print("="*50)
            
            # Step 1: Send open_section message
            print("\n📤 Step 1: Sending open_section message...")
            open_message = {
                "type": "open_section",
                "section_id": 5
            }
            
            await websocket.send(json.dumps(open_message))
            print(f"✅ Sent: {open_message}")
            
            # Step 2: Wait for door status messages
            print("\n📨 Step 2: Waiting for door status messages...")
            door_opened = False
            door_closed = False
            
            for i in range(5):
                try:
                    response_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response_message)
                    print(f"📥 Door status {i+1}: {response_data}")
                    
                    # Check for opened status
                    if response_data.get("status") == "opened":
                        door_opened = True
                        print("🚪 ✅ Door opened!")
                    
                    # Check for closed status
                    if response_data.get("status") == "closed":
                        door_closed = True
                        print("🚪 ✅ Door closed!")
                        break
                    
                except asyncio.TimeoutError:
                    print(f"⏰ Timeout waiting for door status {i+1}")
                    break
            
            if not door_opened or not door_closed:
                print("⚠️  Did not receive expected door status messages")
            
            # Step 3: Send insertion confirmation
            print("\n📤 Step 3: Sending insertion confirmation...")
            insertion_message = {
                "inserted": True
            }
            
            await websocket.send(json.dumps(insertion_message))
            print(f"✅ Sent: {insertion_message}")
            
            # Step 4: Wait for order_send response
            print("\n📨 Step 4: Waiting for order_send response...")
            for i in range(3):
                try:
                    response_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response_message)
                    print(f"📥 Final response {i+1}: {response_data}")
                    
                    # Check for order_send message
                    if "order_send" in response_data:
                        if response_data["order_send"]:
                            print("🎉 ✅ Order send successful!")
                            print(f"   Section IDs: {response_data.get('section_ids', [])}")
                        else:
                            print("❌ Order send failed")
                            print(f"   Error: {response_data.get('message', 'Unknown error')}")
                        break
                    
                except asyncio.TimeoutError:
                    print(f"⏰ Timeout waiting for final response {i+1}")
                    break
            
            print("\n✅ WebSocket flow test completed")
            
    except Exception as e:
        print(f"❌ WebSocket error: {e}")

async def test_stop_selection_flow(session_id):
    """Test the stop_selection flow"""
    print(f"\n🔌 Testing stop_selection flow for session {session_id}...")
    
    try:
        ws_url = f"{WS_BASE_URL}/ws/order/{session_id}"
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected for stop_selection test")
            
            # Wait for initial messages
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    print(f"📥 Initial: {data.get('type', 'unknown')}")
                except asyncio.TimeoutError:
                    break
            
            # Send stop_selection directly (without selecting anything)
            print("\n📤 Sending stop_selection without selecting anything...")
            stop_message = {
                "type": "stop_selection"
            }
            
            await websocket.send(json.dumps(stop_message))
            print(f"✅ Sent: {stop_message}")
            
            # Wait for response
            try:
                response_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response_message)
                print(f"📥 Stop selection response: {response_data}")
                
                if "order_send" in response_data and not response_data["order_send"]:
                    print("✅ Correctly returned order_send: false")
                
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for stop_selection response")
            
    except Exception as e:
        print(f"❌ Stop selection test error: {e}")

async def main():
    """Run all tests"""
    print("🚀 Starting corrected customer send endpoint tests...")
    print("=" * 60)
    
    # Test 1: Basic endpoint test
    session_id = test_customer_send_endpoint()
    
    if session_id:
        # Test 2: Correct WebSocket flow
        await test_websocket_flow(session_id)
        
        # Test 3: Stop selection flow (create new session)
        session_id2 = test_customer_send_endpoint()
        if session_id2:
            await test_stop_selection_flow(session_id2)
    else:
        print("\n⚠️  Skipping WebSocket tests - no session ID available")
    
    print("\n" + "=" * 60)
    print("📊 CORRECT WEBSOCKET FLOW SUMMARY")
    print("=" * 60)
    print("1. Send: {'type': 'open_section', 'section_id': 5}")
    print("2. Receive: {'section_id': 5, 'status': 'opened'}")
    print("3. Receive: {'section_id': 5, 'status': 'closed'}")
    print("4. Send: {'inserted': true}")
    print("5. Receive: {'order_send': true, 'section_ids': [5], 'message': '...'}")
    print("\nOR for stop_selection:")
    print("1. Send: {'type': 'stop_selection'}")
    print("2. Receive: {'order_send': false, 'message': '...'}")

if __name__ == "__main__":
    asyncio.run(main())
