#!/usr/bin/env python3
"""
Test script to verify WebSocket message handling fix for order domain.
Tests the stop_selection message handling specifically.
"""

import asyncio
import json
import websockets
import requests
import time

BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

async def test_stop_selection_message():
    """Test the stop_selection message handling"""
    print("🧪 Testing stop_selection message handling...")
    
    try:
        # 1. Create an order session
        print("📞 Creating order session...")
        response = requests.post(f"{BASE_URL}/order/employment/customer/send", 
                               json={"phone_number": "123456789"})
        
        if response.status_code != 200:
            print(f"❌ Failed to create session: {response.status_code}")
            return False
            
        data = response.json()
        session_id = data.get("session_id")
        
        if not session_id:
            print(f"❌ No session ID returned: {data}")
            return False
            
        print(f"✅ Session created: {session_id}")
        
        # 2. Connect to WebSocket
        print("🔌 Connecting to WebSocket...")
        ws_url = f"{WS_BASE_URL}/ws/order/{session_id}"
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected")
            
            # 3. Wait for initial messages
            print("📨 Waiting for initial messages...")
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    print(f"📥 Received: {data.get('type', 'unknown')} - {data.get('message', '')}")
                except asyncio.TimeoutError:
                    print(f"⏰ Timeout waiting for message {i+1}")
                    break
            
            # 4. Send stop_selection message
            print("📤 Sending stop_selection message...")
            stop_message = {
                "type": "stop_selection",
                "section_id": 1
            }
            
            await websocket.send(json.dumps(stop_message))
            print(f"✅ Sent: {stop_message}")
            
            # 5. Wait for response
            print("📨 Waiting for response...")
            try:
                response_message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                response_data = json.loads(response_message)
                print(f"📥 Response: {response_data.get('type', 'unknown')} - {response_data.get('message', '')}")
                
                # Check if we got a proper response (not an error)
                if response_data.get('type') in ['selection_cancelled', 'selection_completed', 'selection_error']:
                    print("✅ stop_selection message was handled properly!")
                    return True
                else:
                    print(f"⚠️  Unexpected response type: {response_data.get('type')}")
                    return False
                    
            except asyncio.TimeoutError:
                print("❌ Timeout waiting for response to stop_selection")
                return False
                
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

async def test_open_section_message():
    """Test the open_section message handling"""
    print("\n🧪 Testing open_section message handling...")
    
    try:
        # 1. Create an order session
        print("📞 Creating order session...")
        response = requests.post(f"{BASE_URL}/order/employment/customer/send", 
                               json={"phone_number": "555666777"})  # Different phone for no pre-reserved sections
        
        if response.status_code != 200:
            print(f"❌ Failed to create session: {response.status_code}")
            return False
            
        data = response.json()
        session_id = data.get("session_id")
        
        if not session_id:
            print(f"❌ No session ID returned: {data}")
            return False
            
        print(f"✅ Session created: {session_id}")
        
        # 2. Connect to WebSocket
        print("🔌 Connecting to WebSocket...")
        ws_url = f"{WS_BASE_URL}/ws/order/{session_id}"
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected")
            
            # 3. Wait for initial messages
            print("📨 Waiting for initial messages...")
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    print(f"📥 Received: {data.get('type', 'unknown')} - {data.get('message', '')}")
                except asyncio.TimeoutError:
                    print(f"⏰ Timeout waiting for message {i+1}")
                    break
            
            # 4. Send open_section message
            print("📤 Sending open_section message...")
            open_message = {
                "type": "open_section",
                "section_id": 1
            }
            
            await websocket.send(json.dumps(open_message))
            print(f"✅ Sent: {open_message}")
            
            # 5. Wait for responses
            print("📨 Waiting for responses...")
            for i in range(3):
                try:
                    response_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response_message)
                    print(f"📥 Response {i+1}: {response_data.get('type', 'unknown')} - {response_data.get('message', '')}")
                    
                except asyncio.TimeoutError:
                    print(f"⏰ Timeout waiting for response {i+1}")
                    break
            
            # 6. Send stop_selection to finish
            print("📤 Sending stop_selection to finish...")
            stop_message = {
                "type": "stop_selection"
            }
            
            await websocket.send(json.dumps(stop_message))
            
            # Wait for final response
            try:
                response_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response_message)
                print(f"📥 Final response: {response_data.get('type', 'unknown')} - {response_data.get('message', '')}")
                return True
                
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for final response")
                return False
                
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting WebSocket message handling tests...")
    print("=" * 60)
    
    # Test 1: stop_selection message
    result1 = await test_stop_selection_message()
    
    # Test 2: open_section message
    result2 = await test_open_section_message()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"stop_selection test: {'✅ PASSED' if result1 else '❌ FAILED'}")
    print(f"open_section test: {'✅ PASSED' if result2 else '❌ FAILED'}")
    
    if result1 and result2:
        print("\n🎉 All tests passed! WebSocket message handling is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    asyncio.run(main())
