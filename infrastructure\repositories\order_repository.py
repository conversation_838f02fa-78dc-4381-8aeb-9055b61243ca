import mysql.connector
from os import getenv
from typing import List, Dict, Any, Optional
from uuid import uuid4
import logging

logger = logging.getLogger(__name__)

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class OrderRepository:
    """Repository for order database operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_expired_order_sections(self) -> List[int]:
        """
        Get all sections with expired orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting expired order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def get_employee_order_sections(self) -> List[int]:
        """
        Get all sections with employee orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting employee order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def create_employee_delivery_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee delivery.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            pickup_pin = generate_pin()
            
            if pickup_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, pickup_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_deliver', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), pickup_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "pickup_pin": pickup_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee delivery reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    
    def create_employee_send_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee sending order.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            insert_pin = generate_pin()
            
            if insert_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, insert_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_send', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), insert_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "insert_pin": insert_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee send reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    
    def find_reservation_by_pickup_pin(self, pickup_pin: str) -> Optional[Dict[str, Any]]:
        """
        Find order reservation by pickup PIN.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT * FROM order_reservations 
                WHERE pickup_pin = %s AND status = 1 AND expired = 0
            """
            cursor.execute(query, (pickup_pin,))
            return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"Error finding reservation by pickup PIN: {e}")
            return None
        finally:
            cursor.close()
            db.close()
    
    def update_reservation_status(self, reservation_id: int, status: int) -> bool:
        """
        Update reservation status.
        """
        db = get_db()
        cursor = db.cursor()
        try:
            query = """
                UPDATE order_reservations 
                SET status = %s, last_update = NOW()
                WHERE id = %s
            """
            cursor.execute(query, (status, reservation_id))
            db.commit()
            return cursor.rowcount > 0
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error updating reservation status: {e}")
            return False
        finally:
            cursor.close()
            db.close()
    
    def mark_reservation_expired(self, reservation_id: int) -> bool:
        """
        Mark reservation as expired.
        """
        db = get_db()
        cursor = db.cursor()
        try:
            query = """
                UPDATE order_reservations
                SET expired = 1, last_update = NOW()
                WHERE id = %s
            """
            cursor.execute(query, (reservation_id,))
            db.commit()
            return cursor.rowcount > 0
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error marking reservation as expired: {e}")
            return False
        finally:
            cursor.close()
            db.close()

    def find_reservation_by_pin_and_status(self, reservation_pin: int, status: int) -> Dict[str, Any]:
        """
        Find order reservation by reservation pin and status.
        Used for customer send orders (status=8 means ready for insert).
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT id, uuid, box_uuid, section_id, status, insert_pin, pickup_pin,
                       phone_number, size_category, expired, type, last_update, created_at
                FROM order_reservations
                WHERE insert_pin = %s AND status = %s
                ORDER BY created_at DESC
                LIMIT 1
            """
            cursor.execute(query, (str(reservation_pin), status))
            result = cursor.fetchone()

            if result:
                return {
                    "success": True,
                    "reservation": result
                }
            else:
                return {
                    "success": False,
                    "error": "No reservation found with the given PIN and status"
                }
        except Exception as e:
            self.logger.error(f"Error finding reservation by PIN and status: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()

    def update_reservation_section_and_status(self, reservation_id: int, section_id: int, new_status: int) -> bool:
        """
        Update reservation with new section_id and status.
        Used when customer selects a section for their order.
        """
        db = get_db()
        cursor = db.cursor()
        try:
            query = """
                UPDATE order_reservations
                SET section_id = %s, status = %s, last_update = NOW()
                WHERE id = %s
            """
            cursor.execute(query, (str(section_id), new_status, reservation_id))
            db.commit()
            return cursor.rowcount > 0
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error updating reservation section and status: {e}")
            return False
        finally:
            cursor.close()
            db.close()
