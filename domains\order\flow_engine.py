"""
Order Flow Engine.
Defines and manages the sequence of steps for order operations.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class OrderFlowEngine:
    """Manages order flow steps and execution"""
    
    def __init__(self):
        self.steps: List[Dict[str, Any]] = []
        self.current_step_index = 0
        self.completed_steps: List[str] = []
        
    def build_flow(self, flow_config: Dict[str, Any]):
        """Build flow steps from configuration"""
        logger.info(f"Building order flow from config: {flow_config}")
        
        self.steps = []
        operation = flow_config.get("operation")
        
        if operation in ["pickup_expired", "pickup_employee"]:
            # Pickup operations - just hardware step for opening sections
            self.steps.append({
                "type": "pickup_loop",
                "name": "Pickup Loop",
                "context": {
                    "sections": flow_config.get("sections", []),
                    "operation": operation
                }
            })
        elif operation == "deliver_employee":
            # Delivery operation - section selection
            self.steps.append({
                "type": "section_selection",
                "name": "Section Selection",
                "context": {
                    "operation": operation,
                    "phone_number": flow_config.get("phone_number")
                }
            })
        elif operation == "employee_send":
            # Employee send operation - section selection
            self.steps.append({
                "type": "section_selection",
                "name": "Section Selection",
                "context": {
                    "operation": operation,
                    "phone_number": flow_config.get("phone_number"),
                    "reserved_section_id": flow_config.get("reserved_section_id")
                }
            })
        elif operation == "customer_reclaim":
            # Customer reclaim operation - section selection
            self.steps.append({
                "type": "section_selection",
                "name": "Section Selection",
                "context": {
                    "operation": operation,
                    "phone_number": flow_config.get("phone_number"),
                    "reserved_section_id": flow_config.get("reserved_section_id")
                }
            })
        elif operation == "customer_pickup":
            # Customer pickup - hardware step for opening specific section
            self.steps.append({
                "type": "hardware",
                "name": "Open Section for Pickup",
                "context": {
                    "section_id": flow_config.get("section_id"),
                    "operation": "open_for_pickup",
                    "reservation_id": flow_config.get("reservation_id")
                }
            })
        
        logger.info(f"Built order flow with {len(self.steps)} steps: {[s['type'] for s in self.steps]}")
    
    def get_current_step(self) -> Dict[str, Any] | None:
        """Get current step"""
        if self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None
    
    def complete_current_step(self) -> bool:
        """Mark current step as completed"""
        current_step = self.get_current_step()
        if current_step:
            self.completed_steps.append(current_step["type"])
            logger.info(f"Completed step '{current_step['type']}'")
            return True
        return False
    
    def move_to_next_step(self) -> bool:
        """Move to next step"""
        if self.current_step_index < len(self.steps):
            self.current_step_index += 1
            if self.current_step_index < len(self.steps):
                next_step = self.get_current_step()
                logger.info(f"Moved to step {self.current_step_index}: {next_step['type'] if next_step else 'None'}")
                return True
        return False
    
    def is_completed(self) -> bool:
        """Check if all steps are completed"""
        return self.current_step_index >= len(self.steps)
