
Order module documentation

    /order/employment/courier/pickup-expired       # request to pickup expired orders

        request body:
            {
            "operatod_id": 1    # operator id, who is picking up the orders
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # create new websocket session and returns session id
                "success": true,        # if any error occures, returns false
                "message": "Expired orders found",    # success or error message
                "sections": [
                    1,4,6               # all sections with expired orders (retusns all records in order_reservation table section_ids, where expired==0 and status==1 and type=="employee_send")
                ]
                "total_sections": 3      # total number of sections with expired orders
            }

        functionality:
            - if total_sections is greather than 0, it creates session_id, then starts pickup loop
            - employment_pickup_loop(sections)
            - close websocket session



    /order/employment/courier/pickup        # function for courier to pickup orders from employees
        request body:
            {
            "operatod_id": 1    # operator id, who is picking up the orders
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # if total_sections is greather than 0, create new websocket session and returns session id
                "success": true,        # if any error occures or phone number is not valid, returns false
                "message": ""           # success or error message
                "sections": [
                    1,4,6               # all sections with employee's orders (returns all records in order_reservation table section_ids, where expired==0 and status==1 and type=="employee_send")
                ]
                "total_sections": 3     # total number of sections with employee's orders
            }

        functionality:
            - similar to /order/pickup-expired
            - employment_pickup_loop(sections)
            - close websocket session
                


    /order/employment/courier/deliver   # functino to deliver orders to employees, this function is for courier
        request body:
            {
                "phone_number": "123456789"    # employee's phone number
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # if phone number is valid, create new websocket session and returns session id
                "success": true,        # if any error occures or phone number is not valid, returns false
                "message": ""           # success or error message
                "section_ids": [1,2,3]          # reserved section id, if not reserved, returns "section_id": null
            }
            
        functionality:
            - after request, it sends request to jetveo server to "/api/employment/deliver" to check if phone number is valid,
                request: {"phone_number": "123456789"}, response: {"success": true, "phone_number": "123456789","valid": true, "section_ids": null/[1,2,3]}
            - if number is valid, it calls function select_sections():
                success, selected_section_ids = select_sections()
                    - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_ids and "employee_deliver" into type column. Then sends message {"order_deliver": true}.
                    - if success==false, it just sends {"order_deliver": false, "message": some_error_message}
            - closes websocket session



    /order/employment/customer/send 

        request body:
            {
                "phone_number": "123456789"    # employee's phone number
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # create new websocket session and returns session id
                "success": true,        # if any error occures, returns false
                "section_id": 1,        # reserved section id, if not reserved, returns "section_id": null
                "valid": true,          # if phone number is valid, returns true, else returns false
                "message": "Employee notified successfully"    # success or error message
            }

        functionality:
            - after request, it sends request to jetveo server to "/api/employment/send" to check if phone number is valid, request: {"phone_number": "123456789"}, response: {"phone_number": "123456789","valid": true, "section_id": null/1}
            - if phone nubmer is valid, it creates websocket session.
            - calls function select_sections():
                success, selected_section_id = select_sections()
                    - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_id. Then sends message {"order_send": true}
                    - if success==false, it just sends {"order_send": false, "message": some_error_message}
            - closes websocket session


    /order/customer/pickup  ...  # similar to /product/pickup

        request body:
            {
                "pickup_pin": "123456"    # pickup pin
            }

        response body:
            - similar to /product/pickup

        functionality:
            - similar to /product/pickup, it created websockert session and waits for message {"type": "hardware_screen_ready"}
            - for valid pin, check if there is record in order_reservation with matching pin and status==1 and expired==0. check pickup_pin column


            
    /order/employment/customer/reclaim  # reclaim order from customer

        request body:
            {
                "reclamation_pin": "1asfd789"    # employee's phone number
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # create new websocket session and returns session id
                "success": true,        # if any error occures, returns false
                "section_id": 1,        # reserved section id, if not reserved, returns "section_id": null
                "valid": true,          # if phone number is valid, returns true, else returns false
                "message": "Employee notified successfully"    # success or error message
            }

        functionality:
            - after request, it sends request to jetveo server to "/api/employment/reclaim" to check if reservation pin is valid, request: {"reservaiton_pin": "1asfd789"}, response: {"success": true, "phone_number": "123456789","valid": true, "section_id": null/1}
            - if phone nubmer is valid, it creates websocket session.
            - calls function select_sections():
                success, selected_section_id = select_sections()
                    - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_id. Then sends message {"order_send": true}
                    - if success==false, it just sends {"order_send": false, "message": some_error_message}
            - closes websocket session



    /order/customer/send        # function for customer send order to courier

        request body:
        {
            "reservation pin": 123456
        }

        response body:
        {
            "success": true,        # if any error occures, returns false
            "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # create new websocket session and returns session id
            "section_id": 1/null,       # if reserved section exists, returns it, else returns null
            "message": "Order found, ready for pickup"    # success or error message
        }

        functionality:
            - check if in order_reservations is existing record with status==8 (ready for insert) and with the same reservation pin
            - if exists, create new websocket session
            - calls function select_sections(wait_for_stop=false):
                success, selected_section_id = select_sections()
                    - if success==true, it update status of record in order_reservation to 1 and saves selected_section_id. Then send message {"order_send": true}
                    - if success==false, it just sends {"order_send": false, "message": some_error_message}
            - closes websocket session








    USED FUNCITON BASIC LOGIC

    employment_pickup_loop(sections: List[int])
        picking_up = true
        while picking_up:
            message = wait_for_message()   # wait for message from websocket. {"type":"open_section", "section_id": 1} or {"type": "storno"}
            if message["type"] == "open_section":
                open_door(message["section_id"])
                while check_door_state(message["section_id"]) == 1:     # while door is opened
                    wait
                send_message({"section_id": message["section_id"], "status": "closed"})
            elif message["type"] == "storno":
                picking_up = false


    def select_sections(wait_for_stop=true):       # function to choose section, returns success (if section was succesfully selected), section_id (if section was succesfully selected) and error_message (if section was not succesfully selected)

        selected_section_ids = []
        selecting = True
        while selecting
            message = wait_for_message()   # wait for message from websocket. {"type":"open_section", "section_id": 1} or {"type": "stop_selection"}

            if message["type"] == "open_section":
                open_door(message["section_id"])
                send_message({"section_id": message["section_id"], "status": "opened"})
                while check_door_state(message["section_id"]) == 1:     # while door is opened
                    wait
                send_message({"section_id": message["section_id"], "status": "closed"})
                message = wait_for_message()        # {"inserted": true}
                if message["inserted"]:
                    selected_section_ids.append(message["section_id"])
                    return true, selected_section_ids
                    if wait_for_stop:
                        selecting = false
                        return true, selected_section_ids
                elif not message["inserted"]:
                    send_message = {"message": "waiting for new message"}
            
            elif message["type"] == "stop_selection":
                selecting = False
                return false, null



    order_reservation "type" column types:
        employee_send - order, which employee sends to courier
        employee_deliver - order, which courier delivers to customer


    Mock mode valid phone numbers (123456789, 987654321, 555666777)
    Mock mode valid reclamation PINs (RECLAIM123, RECLAIM456)





JETVEO API DOCUMENTATION:

    # checks phone number, when employee wants to send order to courier
    /api/employment/send
        request: {"phone_number": "123456789"}
        response: {"success": true", "phone_number": "123456789","valid": true, "section_id": null/1}

    # checks phone number, when courier wants to deliver order to customer
    /api/employment/deliver
        request: {"phone_number": "123456789"}
        response: {"success": true, "phone_number": "123456789","valid": true, "section_ids": null/[1,2,3]}

    # checks reclamation pin, when customer wants to reclaim order from courier
    /api/employment/reclaim
        request: {"reservation_pin": "1asfd789"}
        response: {"success": true, "phone_number": "123456789","valid": true, "section_id": null/1}